import {
  AdjustmentIcon,
  CalendarIcon,
  DownloadIcon,
  FilterIcon,
} from '@/assets/icons/DashboardIcons'
import Responders from '@/components/dashboard/Responders'
import Ambulances from '@/components/dashboard/Ambulances'
import Hospitals from '@/components/dashboard/Hospitals'
import { SeverityPill } from '@/components/ui/SeverityPill'
import {
  SummaryCardSkeleton,
  CaseTableRowSkeleton,
} from '@/components/ui/Skeleton'
import { Search } from 'lucide-react'
import { useState, useMemo, useEffect } from 'react'
import { DatePickerInput } from '@mantine/dates'
import { Tooltip } from '@mantine/core'
import { useCaseStats } from '@/hooks/api/useCases'
import { useAllResponders } from '@/hooks/api/useResponder'
import { useAmbulanceProviders } from '@/hooks/api/useAmbulance'
import { useAllHospitalsInit } from '@/hooks/api/useHospital'
import { useDispatcher } from '@/providers/DispatcherProvider'
import { subMonths, format } from 'date-fns'
import { ResponderType } from '@/types/api/responders'
import { useDebouncedSearch } from '@/hooks/useDebounce'
import { formatRelativeTime } from '@/utils/userUtils'

type CaseItem = {
  case_created_time: string
  [key: string]: any
}

function generateCaseIds(cases: CaseItem[]): Record<string, string> {
  // Sort cases by created time just to be safe
  const sortedCases = [...cases].sort(
    (a, b) =>
      new Date(a.case_created_time).getTime() -
      new Date(b.case_created_time).getTime()
  )

  const counters: Record<string, number> = {}
  const caseIdMap: Record<string, string> = {}

  sortedCases.forEach(caseItem => {
    const datePart = new Date(caseItem.case_created_time)
      .toISOString()
      .slice(0, 10) // YYYY-MM-DD
      .replace(/-/g, '') // YYYYMMDD

    if (!counters[datePart]) {
      counters[datePart] = 1
    } else {
      counters[datePart]++
    }

    const order = String(counters[datePart]).padStart(4, '0')
    const caseId = `C${datePart}-${order}`

    caseIdMap[caseItem.case_id] = caseId
  })

  return caseIdMap
}

const Dashboard = () => {
  // Date range state - start with null and set after mount
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
    null,
    null,
  ])

  // Set initial date range after component mounts
  useEffect(() => {
    const endDate = new Date()
    const startDate = subMonths(endDate, 1)
    setDateRange([startDate, endDate])
  }, [])

  // Get dispatcher context
  const { dispatcher } = useDispatcher()

  // Safe date formatting function
  const formatDateSafe = (date: Date | null): string | undefined => {
    if (!date || isNaN(date.getTime())) return undefined
    return format(date, 'yyyy-MM-dd')
  }

  // Format dates for API
  const startDate = formatDateSafe(dateRange[0])
  const stopDate = formatDateSafe(dateRange[1])

  // Fetch dashboard data with date range
  const { data: caseStats, isLoading } = useCaseStats(
    dispatcher?.precinct_id,
    startDate,
    stopDate
  )

  // Fetch responders data
  const { data: respondersData, isLoading: isLoadingResponders } =
    useAllResponders(1, 500)

  // Fetch ambulance providers data
  const { data: ambulanceData, isLoading: isLoadingAmbulances } =
    useAmbulanceProviders({
      limit: 10,
      precinct_id: dispatcher?.precinct_id,
    })

  // Fetch hospitals data
  const { data: hospitalsData, isLoading: isLoadingHospitals } =
    useAllHospitalsInit()

  const [casesTab, setCasesTab] = useState('Open Cases')
  const [resourcesTab, setResourcesTab] = useState('Responders')

  // Cases search and filter state
  const {
    searchValue: casesSearchValue,
    debouncedSearchValue: debouncedCasesSearch,
    setSearchValue: setCasesSearchValue,
  } = useDebouncedSearch('', 300)
  const [selectedSeverities, setSelectedSeverities] = useState<string[]>([])
  const [sortOption, setSortOption] = useState<'time' | 'severity'>('time')
  const [showSeverityDropdown, setShowSeverityDropdown] = useState(false)
  const [showSortDropdown, setShowSortDropdown] = useState(false)

  // Prepare summary data from API response
  const summaryData = useMemo(() => {
    if (!caseStats?.cases_count) {
      return [
        { label: 'Total Active Cases', value: 0 },
        { label: 'Pending Ambulance Request', value: 0 },
        { label: 'Average Dispatch Time', value: '0:00' },
        { label: 'Case Resolution', value: 0 },
      ]
    }

    const { cases_count } = caseStats
    return [
      { label: 'Total Active Cases', value: cases_count['in-progress'] || 0 },
      { label: 'Pending Ambulance Request', value: 10 }, // Mock data
      { label: 'Average Dispatch Time', value: '10:00' }, // Mock data
      { label: 'Case Resolution', value: 20 }, // Mock data
    ]
  }, [caseStats])

  // Prepare cases tabs with live data counts
  const casesTabsData = useMemo(() => {
    if (!caseStats?.cases_count) {
      return [
        { name: 'Open Cases', count: 0, key: 'open' },
        { name: 'Active Cases', count: 0, key: 'in-progress' },
        { name: 'Closed Cases', count: 0, key: 'closed' },
      ]
    }

    const { cases_count } = caseStats
    return [
      { name: 'Open Cases', count: cases_count.open || 0, key: 'open' },
      {
        name: 'Active Cases',
        count: cases_count['in-progress'] || 0,
        key: 'in-progress',
      },
      { name: 'Closed Cases', count: cases_count.closed || 0, key: 'closed' },
    ]
  }, [caseStats])

  // Get raw cases data for the current tab
  const rawCurrentTabData = useMemo(() => {
    if (!caseStats?.result) return []

    const currentTab = casesTabsData.find(tab => tab.name === casesTab)
    if (!currentTab) return []

    return (
      caseStats.result[currentTab.key as keyof typeof caseStats.result] || []
    )
  }, [caseStats, casesTab, casesTabsData])

  // Generate case IDs for the raw data
  const generatedCaseIds = useMemo(() => {
    if (!rawCurrentTabData.length) return {}
    return generateCaseIds(rawCurrentTabData)
  }, [rawCurrentTabData])

  // Get processed cases data with sorting, filtering, and searching
  const currentTabData = useMemo(() => {
    let cases = [...rawCurrentTabData]

    // Apply search filter
    if (debouncedCasesSearch.trim()) {
      const searchTerm = debouncedCasesSearch.toLowerCase()
      cases = cases.filter(caseItem => {
        const caseId = generatedCaseIds[caseItem.case_id] || caseItem.case_id
        const description = caseItem.incident_description || ''
        const location = caseItem.address_line_1 || ''

        return (
          caseId.toLowerCase().includes(searchTerm) ||
          description.toLowerCase().includes(searchTerm) ||
          location.toLowerCase().includes(searchTerm)
        )
      })
    }

    // Apply severity filter
    if (selectedSeverities.length > 0 && selectedSeverities.length < 3) {
      cases = cases.filter(caseItem =>
        selectedSeverities.includes(caseItem.severity?.toLowerCase() || '')
      )
    }

    // Apply sorting
    cases = [...cases].sort((a, b) => {
      if (sortOption === 'time') {
        // Sort by case_created_time descending (most recent first)
        return (
          new Date(b.case_created_time).getTime() -
          new Date(a.case_created_time).getTime()
        )
      } else if (sortOption === 'severity') {
        // Sort by severity: high -> moderate -> low, then by time within each severity
        const severityOrder = { high: 3, moderate: 2, low: 1 }
        const aSeverity =
          severityOrder[
            a.severity?.toLowerCase() as keyof typeof severityOrder
          ] || 0
        const bSeverity =
          severityOrder[
            b.severity?.toLowerCase() as keyof typeof severityOrder
          ] || 0

        if (aSeverity !== bSeverity) {
          return bSeverity - aSeverity // Higher severity first
        }

        // Same severity, sort by time descending
        return (
          new Date(b.case_created_time).getTime() -
          new Date(a.case_created_time).getTime()
        )
      }

      return 0
    })

    return cases
  }, [
    rawCurrentTabData,
    debouncedCasesSearch,
    selectedSeverities,
    sortOption,
    generatedCaseIds,
  ])

  // Event handlers for dropdowns and search
  const handleSeverityToggle = (severity: string) => {
    setSelectedSeverities(prev => {
      const newSelection = prev.includes(severity)
        ? prev.filter(s => s !== severity)
        : [...prev, severity]

      // If all three are selected, clear the filter
      if (newSelection.length === 3) {
        return []
      }

      return newSelection
    })
  }

  const handleSortChange = (option: 'time' | 'severity') => {
    setSortOption(option)
    setShowSortDropdown(false)
  }

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (showSeverityDropdown && !target.closest('.severity-dropdown')) {
        setShowSeverityDropdown(false)
      }
      if (showSortDropdown && !target.closest('.sort-dropdown')) {
        setShowSortDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [showSeverityDropdown, showSortDropdown])

  return (
    <div className="w-full font-body flex flex-col h-[calc(100vh-100px)]">
      <div className="h-fit flex justify-between items-center mb-6">
        <div className="font-bold font-heading">Summary</div>

        <div className="flex items-center gap-2 font-medium text-xs">
          {/* Date range filter */}
          <div className="bg-white flex items-center gap-2 px-3 py-1.5 rounded-lg shadow hover:shadow-lg border-[0.5px] border-gray-80">
            <CalendarIcon />
            <DatePickerInput
              type="range"
              value={dateRange}
              onChange={value => {
                // Convert string dates to Date objects
                const [start, end] = value
                const startDate = start ? new Date(start) : null
                const endDate = end ? new Date(end) : null
                setDateRange([startDate, endDate])
              }}
              valueFormat="YYYY-MM-DD"
              placeholder="Select date range"
              size="xs"
              variant="unstyled"
              className="text-xs"
              styles={{
                input: {
                  border: 'none',
                  padding: 0,
                  fontSize: '12px',
                  fontWeight: 500,
                  minHeight: 'auto',
                  height: 'auto',
                },
              }}
            />
          </div>

          {/* Export dashboard metrics */}
          <div className="bg-white flex items-center gap-2 cursor-pointer px-3 py-1.5 rounded-lg shadow hover:shadow-lg border-[0.5px] border-gray-80">
            <DownloadIcon />
            <span>Export</span>
          </div>
        </div>
      </div>

      {/* Summary Section */}
      <div className="h-fit grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        {isLoading
          ? // Show skeleton loading for summary cards
            Array.from({ length: 4 }).map((_, index) => (
              <SummaryCardSkeleton key={index} />
            ))
          : summaryData.map(({ label, value }) => (
              <div
                key={label}
                className="bg-white p-4 rounded-lg shadow-sm border border-gray-200"
              >
                <div className="text-sm font-medium whitespace-nowrap overflow-hidden text-ellipsis">
                  {label}
                </div>
                <div className="text-[40px] font-medium">{value}</div>
              </div>
            ))}
      </div>

      <section className="h-fit flex-grow grid grid-cols-1 lg:grid-cols-3 gap-y-4 lg:gap-4 min-h-0">
        {/* LHS - Cases Section */}
        <div className="col-span-2 flex flex-col h-full min-h-0">
          <div className="h-fit flex items-center gap-2 font-medium text-xs mb-2">
            {casesTabsData.map(item => (
              <div
                key={item.name}
                className={`${
                  casesTab === item.name
                    ? 'bg-blue-10 text-dark-gray-10'
                    : 'bg-white text-dark-gray-40 hover:shadow-lg'
                } flex items-center gap-2 cursor-pointer px-3 py-1.5 rounded-lg shadow border-[0.5px] border-gray-80`}
                onClick={() => setCasesTab(item.name)}
              >
                <span>{item.name}</span>
                {!isLoading && (
                  <span className="bg-gray-200 text-gray-700 px-2 py-0.5 rounded-full text-xs">
                    {item.count}
                  </span>
                )}
              </div>
            ))}
          </div>

          {/* Cases Section - parent handles scroll */}
          <div className="flex-grow overflow-y-auto min-h-0 bg-white rounded-lg border-[0.5px] border-gray-80">
            <div className="flex items-center gap-4 px-4 py-3">
              {/* Search Bar */}
              <div className="relative w-full text-xs bg-light-gray-10">
                <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                <input
                  type="text"
                  placeholder="Search Cases"
                  value={casesSearchValue}
                  onChange={e => setCasesSearchValue(e.target.value)}
                  className="w-full h-[30px] rounded-[128px] border-[0.5px] border-gray-80 py-2 pl-7 pr-4 placeholder:text-gray-500 focus:border-primary focus:outline-none"
                />
              </div>

              {/* Filter and Sort */}
              <div className="flex items-center gap-2 font-medium text-xs">
                {/* Severity Filter */}
                <div className="severity-dropdown relative">
                  <div
                    className="bg-white flex items-center gap-2 cursor-pointer px-3 py-1.5 rounded-lg shadow hover:shadow-lg border-[0.5px] border-gray-80"
                    onClick={() =>
                      setShowSeverityDropdown(!showSeverityDropdown)
                    }
                  >
                    <FilterIcon />
                    <span>Filter</span>
                    {selectedSeverities.length > 0 && (
                      <span className="bg-primary text-white px-1.5 py-0.5 rounded-full text-xs">
                        {selectedSeverities.length}
                      </span>
                    )}
                  </div>

                  {showSeverityDropdown && (
                    <div className="absolute right-0 top-full z-10 mt-1 w-48 rounded-lg border border-gray-200 bg-white shadow-lg">
                      <div className="p-3">
                        <div className="text-sm font-medium mb-2">
                          Severity Level
                        </div>
                        {['high', 'moderate', 'low'].map(severity => (
                          <label
                            key={severity}
                            className="flex items-center font-normal gap-2 py-1 cursor-pointer"
                          >
                            <input
                              type="checkbox"
                              checked={selectedSeverities.includes(severity)}
                              onChange={() => handleSeverityToggle(severity)}
                              className="rounded border-gray-300"
                            />
                            <span className="capitalize text-sm">
                              {severity}
                            </span>
                          </label>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Sort Dropdown */}
                <div className="sort-dropdown relative">
                  <div
                    className="bg-white flex items-center gap-2 cursor-pointer px-3 py-1.5 rounded-lg shadow hover:shadow-lg border-[0.5px] border-gray-80"
                    onClick={() => setShowSortDropdown(!showSortDropdown)}
                  >
                    <AdjustmentIcon />
                    <span>Sort</span>
                  </div>

                  {showSortDropdown && (
                    <div className="absolute right-0 top-full z-10 mt-1 w-48 rounded-lg border border-gray-200 bg-white shadow-lg">
                      <div className="p-2">
                        <div className="text-sm font-medium mb-2">
                          Sort by
                        </div>
                        <div className="space-y-1">
                          <button
                            className={`w-full text-left font-normal px-2 py-1 rounded text-sm hover:bg-gray-100 ${
                              sortOption === 'time'
                                ? 'bg-primary/10 text-primary'
                                : ''
                            }`}
                            onClick={() => handleSortChange('time')}
                          >
                            Request Time
                          </button>
                          <button
                            className={`w-full text-left px-2 py-1 rounded text-sm hover:bg-gray-100 ${
                              sortOption === 'severity'
                                ? 'bg-primary/10 text-primary'
                                : ''
                            }`}
                            onClick={() => handleSortChange('severity')}
                          >
                            Severity
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Cases table */}
            <div className="overflow-x-auto">
              <table className="w-full table-fixed">
                <thead>
                  <tr className="border-y border-gray-200">
                    <th className="w-36 px-4 py-3 text-left text-sm font-semibold text-gray-700">
                      Case ID
                    </th>
                    <th className="w-64 px-4 py-3 text-left text-sm font-semibold text-gray-700">
                      Description
                    </th>
                    <th className="w-48 px-4 py-3 text-left text-sm font-semibold text-gray-700">
                      Location
                    </th>
                    <th className="w-40 px-4 py-3 text-left text-sm font-semibold text-gray-700">
                      Request Time
                    </th>
                    <th className="w-32 px-4 py-3 text-center text-sm font-semibold text-gray-700">
                      Severity Level
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {isLoading ? (
                    Array.from({ length: 5 }).map((_, index) => (
                      <CaseTableRowSkeleton key={index} />
                    ))
                  ) : currentTabData.length > 0 ? (
                    currentTabData.map(caseItem => {
                      const caseId =
                        generatedCaseIds[caseItem.case_id] ?? caseItem.case_id
                      const description = caseItem.incident_description || ''
                      const location = caseItem.address_line_1 || ''
                      const requestTime = caseItem.case_created_time || ''

                      return (
                        <tr
                          key={caseItem.case_id}
                          className="border-b border-gray-200 hover:bg-gray-50"
                        >
                          <Tooltip label={caseId} position="top" withArrow>
                            <td className="w-32 px-4 py-3 text-xs text-primary font-medium truncate">
                              {caseId}
                            </td>
                          </Tooltip>
                          <Tooltip label={description} position="top" withArrow>
                            <td className="w-64 px-4 py-3 text-xs text-gray-800 truncate">
                              {description}
                            </td>
                          </Tooltip>
                          <Tooltip label={location} position="top" withArrow>
                            <td className="w-48 px-4 py-3 text-xs text-gray-800 truncate">
                              {location}
                            </td>
                          </Tooltip>
                          <Tooltip
                            label={formatRelativeTime(requestTime)}
                            position="top"
                            withArrow
                          >
                            <td className="w-40 px-4 py-3 text-xs text-gray-800 truncate">
                              {formatRelativeTime(requestTime)}
                            </td>
                          </Tooltip>
                          <td className="w-32 px-4 py-3 text-xs text-center text-gray-800">
                            <SeverityPill level={caseItem.severity} />
                          </td>
                        </tr>
                      )
                    })
                  ) : (
                    <tr>
                      <td
                        colSpan={5}
                        className="h-[400px] text-sm text-gray-500"
                      >
                        <div className="flex items-center justify-center h-full w-full">
                          {debouncedCasesSearch.trim() ||
                          selectedSeverities.length > 0
                            ? 'No cases match your search criteria'
                            : 'No cases found for the selected period'}
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* RHS - Resources Section */}
        <div className="col-span-1 flex flex-col h-full min-h-0">
          <div className="h-fit font-heading font-bold leading-[25px] text-sm mb-4">
            Resources
          </div>

          <div className="flex-grow bg-white px-4 py-3 rounded-lg border-[0.5px] border-gray-80 flex flex-col h-full min-h-0">
            <div className="h-fit w-full flex items-center justify-between font-medium text-xs mb-3 bg-light-blueOne p-1 rounded-lg">
              {['Responders', 'Ambulances', 'Hospitals'].map(item => (
                <div
                  key={item}
                  className={`${
                    resourcesTab === item
                      ? 'bg-white shadow'
                      : 'hover:shadow-lg'
                  } text-dark-blueOne flex items-center gap-2 cursor-pointer px-4 py-1.5 rounded-lg`}
                  onClick={() => setResourcesTab(item)}
                >
                  <span>{item}</span>
                </div>
              ))}
            </div>

            {/* Parent container now controls scroll */}
            <div className="flex-grow overflow-y-auto min-h-0" id="parent">
              {resourcesTab === 'Responders' && (
                <Responders
                  responders={(respondersData?.items as ResponderType[]) || []}
                  isLoading={isLoadingResponders}
                />
              )}
              {resourcesTab === 'Ambulances' && (
                <Ambulances
                  ambulances={ambulanceData?.ambulance_providers || []}
                  isLoading={isLoadingAmbulances}
                />
              )}
              {resourcesTab === 'Hospitals' && (
                <Hospitals
                  hospitals={hospitalsData || []}
                  isLoading={isLoadingHospitals}
                />
              )}
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Dashboard

import {
  AdjustmentIcon,
  CalendarIcon,
  DownloadIcon,
  FilterIcon,
} from '@/assets/icons/DashboardIcons'
import Responders from '@/components/dashboard/Responders'
import Ambulances from '@/components/dashboard/Ambulances'
import Hospitals from '@/components/dashboard/Hospitals'
import { SeverityPill } from '@/components/ui/SeverityPill'
import {
  SummaryCardSkeleton,
  CaseTableRowSkeleton,
} from '@/components/ui/Skeleton'
import { Search } from 'lucide-react'
import { useState, useMemo, useEffect } from 'react'
import { DatePickerInput } from '@mantine/dates'
import { useCaseStats } from '@/hooks/api/useCases'
import { useAllResponders } from '@/hooks/api/useResponder'
import { useAmbulanceProviders } from '@/hooks/api/useAmbulance'
import { useAllHospitalsInit } from '@/hooks/api/useHospital'
import { useDispatcher } from '@/providers/DispatcherProvider'
import { subMonths, format } from 'date-fns'
import { ResponderType } from '@/types/api/responders'

type CaseItem = {
  case_created_time: string
  [key: string]: any
}

function generateCaseIds(cases: CaseItem[]): Record<string, string> {
  // Sort cases by created time just to be safe
  const sortedCases = [...cases].sort(
    (a, b) =>
      new Date(a.case_created_time).getTime() -
      new Date(b.case_created_time).getTime()
  )

  const counters: Record<string, number> = {}
  const caseIdMap: Record<string, string> = {}

  sortedCases.forEach(caseItem => {
    const datePart = new Date(caseItem.case_created_time)
      .toISOString()
      .slice(0, 10) // YYYY-MM-DD
      .replace(/-/g, '') // YYYYMMDD

    if (!counters[datePart]) {
      counters[datePart] = 1
    } else {
      counters[datePart]++
    }

    const order = String(counters[datePart]).padStart(4, '0')
    const caseId = `C${datePart}-${order}`

    caseIdMap[caseItem.case_id] = caseId
  })

  return caseIdMap
}

const Dashboard = () => {
  // Date range state - start with null and set after mount
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
    null,
    null,
  ])

  // Set initial date range after component mounts
  useEffect(() => {
    const endDate = new Date()
    const startDate = subMonths(endDate, 1)
    setDateRange([startDate, endDate])
  }, [])

  // Get dispatcher context
  const { dispatcher } = useDispatcher()

  // Safe date formatting function
  const formatDateSafe = (date: Date | null): string | undefined => {
    if (!date || isNaN(date.getTime())) return undefined
    return format(date, 'yyyy-MM-dd')
  }

  // Format dates for API
  const startDate = formatDateSafe(dateRange[0])
  const stopDate = formatDateSafe(dateRange[1])

  // Fetch dashboard data with date range
  const { data: caseStats, isLoading } = useCaseStats(
    dispatcher?.precinct_id,
    startDate,
    stopDate
  )

  // Fetch responders data
  const { data: respondersData, isLoading: isLoadingResponders } =
    useAllResponders(1, 500)

  // Fetch ambulance providers data
  const { data: ambulanceData, isLoading: isLoadingAmbulances } =
    useAmbulanceProviders({
      limit: 10,
      precinct_id: dispatcher?.precinct_id,
    })

  // Fetch hospitals data
  const { data: hospitalsData, isLoading: isLoadingHospitals } =
    useAllHospitalsInit()

  const [casesTab, setCasesTab] = useState('Open Cases')
  const [resourcesTab, setResourcesTab] = useState('Responders')

  // Prepare summary data from API response
  const summaryData = useMemo(() => {
    if (!caseStats?.cases_count) {
      return [
        { label: 'Total Active Cases', value: 0 },
        { label: 'Pending Ambulance Request', value: 0 },
        { label: 'Average Dispatch Time', value: '0:00' },
        { label: 'Case Resolution', value: 0 },
      ]
    }

    const { cases_count } = caseStats
    return [
      { label: 'Total Active Cases', value: cases_count['in-progress'] || 0 },
      { label: 'Pending Ambulance Request', value: 10 }, // Mock data
      { label: 'Average Dispatch Time', value: '10:00' }, // Mock data
      { label: 'Case Resolution', value: 20 }, // Mock data
    ]
  }, [caseStats])

  // Prepare cases tabs with live data counts
  const casesTabsData = useMemo(() => {
    if (!caseStats?.cases_count) {
      return [
        { name: 'Open Cases', count: 0, key: 'open' },
        { name: 'Active Cases', count: 0, key: 'in-progress' },
        { name: 'Closed Cases', count: 0, key: 'closed' },
      ]
    }

    const { cases_count } = caseStats
    return [
      { name: 'Open Cases', count: cases_count.open || 0, key: 'open' },
      {
        name: 'Active Cases',
        count: cases_count['in-progress'] || 0,
        key: 'in-progress',
      },
      { name: 'Closed Cases', count: cases_count.closed || 0, key: 'closed' },
    ]
  }, [caseStats])

  // Get current tab cases data
  const currentTabData = useMemo(() => {
    if (!caseStats?.result) return []

    const currentTab = casesTabsData.find(tab => tab.name === casesTab)
    if (!currentTab) return []

    return (
      caseStats.result[currentTab.key as keyof typeof caseStats.result] || []
    )
  }, [caseStats, casesTab, casesTabsData])

  const generatedCaseIds = useMemo(() => {
    if (!currentTabData.length) return {}
    return generateCaseIds(currentTabData)
  }, [currentTabData])

  return (
    <div className="w-full font-body flex flex-col h-[calc(100vh-100px)]">
      <div className="h-fit flex justify-between items-center mb-6">
        <div className="font-bold font-heading">Summary</div>

        <div className="flex items-center gap-2 font-medium text-xs">
          {/* Date range filter */}
          <div className="bg-white flex items-center gap-2 px-3 py-1.5 rounded-lg shadow hover:shadow-lg border-[0.5px] border-gray-80">
            <CalendarIcon />
            <DatePickerInput
              type="range"
              value={dateRange}
              onChange={value => {
                // Convert string dates to Date objects
                const [start, end] = value
                const startDate = start ? new Date(start) : null
                const endDate = end ? new Date(end) : null
                setDateRange([startDate, endDate])
              }}
              valueFormat="YYYY-MM-DD"
              placeholder="Select date range"
              size="xs"
              variant="unstyled"
              className="text-xs"
              styles={{
                input: {
                  border: 'none',
                  padding: 0,
                  fontSize: '12px',
                  fontWeight: 500,
                  minHeight: 'auto',
                  height: 'auto',
                },
              }}
            />
          </div>

          {/* Export dashboard metrics */}
          <div className="bg-white flex items-center gap-2 cursor-pointer px-3 py-1.5 rounded-lg shadow hover:shadow-lg border-[0.5px] border-gray-80">
            <DownloadIcon />
            <span>Export</span>
          </div>
        </div>
      </div>

      {/* Summary Section */}
      <div className="h-fit grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        {isLoading
          ? // Show skeleton loading for summary cards
            Array.from({ length: 4 }).map((_, index) => (
              <SummaryCardSkeleton key={index} />
            ))
          : summaryData.map(({ label, value }) => (
              <div
                key={label}
                className="bg-white p-4 rounded-lg shadow-sm border border-gray-200"
              >
                <div className="text-sm font-medium whitespace-nowrap overflow-hidden text-ellipsis">
                  {label}
                </div>
                <div className="text-[40px] font-medium">{value}</div>
              </div>
            ))}
      </div>

      <section className="h-fit flex-grow grid grid-cols-1 lg:grid-cols-3 gap-y-4 lg:gap-4 min-h-0">
        {/* LHS - Cases Section */}
        <div className="col-span-2 flex flex-col h-full min-h-0">
          <div className="h-fit flex items-center gap-2 font-medium text-xs mb-2">
            {casesTabsData.map(item => (
              <div
                key={item.name}
                className={`${
                  casesTab === item.name
                    ? 'bg-blue-10 text-dark-gray-10'
                    : 'bg-white text-dark-gray-40 hover:shadow-lg'
                } flex items-center gap-2 cursor-pointer px-3 py-1.5 rounded-lg shadow border-[0.5px] border-gray-80`}
                onClick={() => setCasesTab(item.name)}
              >
                <span>{item.name}</span>
                {!isLoading && (
                  <span className="bg-gray-200 text-gray-700 px-2 py-0.5 rounded-full text-xs">
                    {item.count}
                  </span>
                )}
              </div>
            ))}
          </div>

          {/* Cases Section - parent handles scroll */}
          <div className="flex-grow overflow-y-auto min-h-0 bg-white rounded-lg border-[0.5px] border-gray-80">
            <div className="flex items-center gap-4 px-4 py-3">
              {/* Search Bar */}
              <div className="relative w-full text-xs bg-light-gray-10">
                <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                <input
                  type="text"
                  placeholder="Search Cases"
                  className="w-full h-[30px] rounded-[128px] border-[0.5px] border-gray-80 py-2 pl-7 pr-4 placeholder:text-gray-500 focus:border-primary focus:outline-none"
                />
              </div>

              {/* Filter and Sort */}
              <div className="flex items-center gap-2 font-medium text-xs">
                {/* Cases Filter */}
                <div className="bg-white flex items-center gap-2 cursor-pointer px-3 py-1.5 rounded-lg shadow hover:shadow-lg border-[0.5px] border-gray-80">
                  <FilterIcon />
                  <span>Filter</span>
                </div>

                {/* Cases Sort */}
                <div className="bg-white flex items-center gap-2 cursor-pointer px-3 py-1.5 rounded-lg shadow hover:shadow-lg border-[0.5px] border-gray-80">
                  <AdjustmentIcon />
                  <span>Sort</span>
                </div>
              </div>
            </div>

            {/* Cases table */}
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-y border-gray-200">
                    <th className="px-4 py-3 text-left text-sm font-semibold text-gray-700">
                      Case ID
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-semibold text-gray-700">
                      Description
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-semibold text-gray-700">
                      Location
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-semibold text-gray-700">
                      Request Time
                    </th>
                    <th className="px-4 py-3 text-center text-sm font-semibold text-gray-700">
                      Severity Level
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {isLoading ? (
                    Array.from({ length: 5 }).map((_, index) => (
                      <CaseTableRowSkeleton key={index} />
                    ))
                  ) : currentTabData.length > 0 ? (
                    currentTabData.map(caseItem => (
                      <tr
                        key={caseItem.case_id}
                        className="border-b border-gray-200"
                      >
                        <td className="px-4 py-3 text-xs text-primary font-medium whitespace-nowrap">
                          {generatedCaseIds[caseItem.case_id] ??
                            caseItem.case_id}
                        </td>
                        <td className="px-4 py-3 text-xs text-gray-800 whitespace-nowrap">
                          {caseItem.incident_description}
                        </td>
                        <td className="px-4 py-3 text-xs text-gray-800 whitespace-nowrap">
                          {caseItem.address_line_1}
                        </td>
                        <td className="px-4 py-3 text-xs text-gray-800 whitespace-nowrap">
                          {caseItem.case_created_time}
                        </td>
                        <td className="px-4 py-3 text-xs text-center text-gray-800 whitespace-nowrap">
                          <SeverityPill level={caseItem.severity} />
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td
                        colSpan={5}
                        className="h-[400px] text-sm text-gray-500"
                      >
                        <div className="flex items-center justify-center h-full w-full">
                          No cases found for the selected period
                        </div>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* RHS - Resources Section */}
        <div className="col-span-1 flex flex-col h-full min-h-0">
          <div className="h-fit font-heading font-bold leading-[25px] text-sm mb-4">
            Resources
          </div>

          <div className="flex-grow bg-white px-4 py-3 rounded-lg border-[0.5px] border-gray-80 flex flex-col h-full min-h-0">
            <div className="h-fit w-full flex items-center justify-between font-medium text-xs mb-3 bg-light-blueOne p-1 rounded-lg">
              {['Responders', 'Ambulances', 'Hospitals'].map(item => (
                <div
                  key={item}
                  className={`${
                    resourcesTab === item
                      ? 'bg-white shadow'
                      : 'hover:shadow-lg'
                  } text-dark-blueOne flex items-center gap-2 cursor-pointer px-4 py-1.5 rounded-lg`}
                  onClick={() => setResourcesTab(item)}
                >
                  <span>{item}</span>
                </div>
              ))}
            </div>

            {/* Parent container now controls scroll */}
            <div className="flex-grow overflow-y-auto min-h-0" id="parent">
              {resourcesTab === 'Responders' && (
                <Responders
                  responders={(respondersData?.items as ResponderType[]) || []}
                  isLoading={isLoadingResponders}
                />
              )}
              {resourcesTab === 'Ambulances' && (
                <Ambulances
                  ambulances={ambulanceData?.ambulance_providers || []}
                  isLoading={isLoadingAmbulances}
                />
              )}
              {resourcesTab === 'Hospitals' && (
                <Hospitals
                  hospitals={hospitalsData || []}
                  isLoading={isLoadingHospitals}
                />
              )}
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Dashboard

interface SeverityPillProps {
  level: 'low' | 'medium' | 'high' | string
}

const pillColor = (level: string) => {
  switch (level) {
    case 'low':
      return 'bg-[#E6E2E1] text-black'
    case 'moderate':
      return 'bg-[#FFCC00] text-black'
    case 'high':
      return 'bg-[#F31222] text-white'
    default:
      return 'bg-[#E6E2E1] text-black'
  }
}
export function SeverityPill({ level }: SeverityPillProps) {
  return (
    <span
      className={`${pillColor(level)} px-2 py-1 text-xs font-medium rounded capitalize`}
    >
      {level}
    </span>
  )
}

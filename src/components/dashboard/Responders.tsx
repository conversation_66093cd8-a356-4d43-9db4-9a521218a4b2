import { useEffect, useState } from 'react'
import { ChevronDown, Search, UserSearch, X } from 'lucide-react'
import { Modal } from '@mantine/core'
import { ResponderType } from '@/types/api/responders'
import { ResourceTabSkeleton } from '@/components/ui/Skeleton'
import { useDebouncedSearch } from '@/hooks/useDebounce'

const responderTypes = [
  'All Responders',
  'Ambulance Transporter',
  'First Responder',
]

const getResponderGroup = (value?: string) => {
  if (!value) return ''
  switch (value) {
    case 'ambulance-transporter':
      return 'Ambulance Transporter'
    case 'paramedic':
      return 'Comm. Based First Responder'
    default:
      return value
  }
}

const Responders = ({
  responders,
  isLoading,
}: {
  responders: ResponderType[]
  isLoading: boolean
}) => {
  const [filteredResponders, setFilteredResponders] = useState<ResponderType[]>(
    []
  )
  const [showResponderDropdown, setShowResponderDropdown] = useState(false)
  const [selectedResponder, setSelectedResponder] =
    useState<string>('All Responders')
  const [selectedResponderForModal, setSelectedResponderForModal] =
    useState<ResponderType | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  // Search functionality
  const {
    searchValue: searchQuery,
    debouncedSearchValue: debouncedSearch,
    setSearchValue: setSearchQuery,
  } = useDebouncedSearch('', 300)

  // Apply filters and search
  useEffect(() => {
    let filtered = [...responders]

    // Apply responder type filter
    if (selectedResponder !== 'All Responders') {
      if (selectedResponder === 'Ambulance Transporter') {
        filtered = filtered.filter(
          item => item?.responder_type === 'ambulance-transporter'
        )
      } else {
        filtered = filtered.filter(
          item => item?.responder_type !== 'ambulance-transporter'
        )
      }
    }

    // Apply search filter
    if (debouncedSearch.trim()) {
      const searchTerm = debouncedSearch.toLowerCase()
      filtered = filtered.filter(item => {
        const firstName = item?.first_name?.toLowerCase() || ''
        const lastName = item?.last_name?.toLowerCase() || ''
        const phoneNumber = item?.phone_number?.toLowerCase() || ''

        return (
          firstName.includes(searchTerm) ||
          lastName.includes(searchTerm) ||
          phoneNumber.includes(searchTerm)
        )
      })
    }

    setFilteredResponders(filtered)
  }, [responders, selectedResponder, debouncedSearch])

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (showResponderDropdown && !target.closest('.responder-dropdown')) {
        setShowResponderDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [showResponderDropdown])

  const handleResponderSelect = (type: string) => {
    setSelectedResponder(type)
    setShowResponderDropdown(false)
  }

  const handleViewMore = (responder: ResponderType) => {
    setSelectedResponderForModal(responder)
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    setSelectedResponderForModal(null)
  }

  return (
    <div className="text-xs w-full">
      <div className="flex items-center justify-between mb-4">
        <div>
          All:{' '}
          <span className="text-primary font-semibold">
            {filteredResponders.length}
          </span>
        </div>

        {/* Responder Type Filter */}
        <div className="responder-dropdown relative">
          <div
            onClick={() => setShowResponderDropdown(!showResponderDropdown)}
            className="flex h-7 items-center gap-2 rounded-lg border-[0.5px] border-gray-80 px-3 py-1.5 text-dark-gray-30 transition-colors hover:shadow-lg cursor-pointer"
          >
            <span className="truncate">{selectedResponder}</span>
            <ChevronDown
              className={`h-4 w-4 transition-transform ${showResponderDropdown ? 'rotate-180' : ''}`}
            />
          </div>

          {showResponderDropdown && (
            <div className="absolute right-0 top-full z-10 mt-1 rounded-lg border border-gray-200 bg-white shadow-lg">
              <div className="max-h-60 overflow-y-auto py-1">
                {responderTypes.map(type => (
                  <button
                    key={type}
                    onClick={() => handleResponderSelect(type)}
                    className={`cursor-pointer w-full px-4 py-2 text-left hover:bg-light-blueOne ${
                      selectedResponder === type
                        ? 'bg-primary/10 text-primary'
                        : 'text-dark-gray-30'
                    }`}
                  >
                    {type}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Search Bar */}
      <div className="relative w-full mb-4">
        <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
        <input
          type="text"
          placeholder="Search Responders"
          value={searchQuery}
          onChange={e => setSearchQuery(e.target.value)}
          className="w-full h-[30px] rounded-lg border-[0.5px] border-gray-80 py-2 pl-7 pr-4 placeholder:text-gray-500 focus:border-primary focus:outline-none"
        />
      </div>
      {isLoading ? (
        <ResourceTabSkeleton />
      ) : filteredResponders?.length > 0 ? (
        filteredResponders?.map((item: ResponderType, index: any) => {
          const responderFullName = `${item?.first_name || ''} \n ${
            item?.last_name || ''
          }`
          let responderName = responderFullName
          if (responderName?.length >= 22) {
            responderName = `${responderName.substring(0, 22)}`
          }
          return (
            <div
              key={index}
              className="bg-off-white flex items-center justify-between py-2 px-2 rounded-lg mb-2"
            >
              <div className="flex items-center gap-3">
                <div className="flex">
                  <div className="w-10 h-10 rounded-full">
                    <img
                      src={
                        item?.profile_image_url ||
                        '//style.anu.edu.au/_anu/4/images/placeholders/person.png'
                      }
                      className="w-10 h-10 rounded-full object-cover"
                      alt="Profile"
                      onError={(image: any) => {
                        image.target.src =
                          '//style.anu.edu.au/_anu/4/images/placeholders/person.png'
                      }}
                    />
                  </div>

                  <div
                    className={`h-3.5 w-3.5 -ml-3 -mt-1 rounded-full border-2 border-white ${
                      item?.status === 'online'
                        ? 'bg-era-green'
                        : item?.status === 'engaged'
                          ? 'bg-red-500'
                          : 'bg-gray-400'
                    }`}
                  />
                </div>

                <div>
                  <div className="text-off-black font-semibold">
                    {responderName}
                  </div>

                  <div className="text-lighter-gray font-light capitalize">
                    {getResponderGroup(item?.responder_type) || ''}
                    {item?.state && `, ${item?.state}`}
                  </div>
                </div>
              </div>

              <div className="-mt-2">
                <div
                  className="whitespace-nowrap bg-white flex items-center gap-2 cursor-pointer px-2 py-1.5 rounded-lg shadow hover:shadow-lg border-[0.5px] border-gray-80 font-medium"
                  onClick={() => handleViewMore(item)}
                >
                  View More
                </div>
              </div>
            </div>
          )
        })
      ) : (
        <div className="w-full h-full py-10 flex flex-col justify-center items-center text-gray-500 font-medium">
          <UserSearch className="mb-4" />
          <p>
            {debouncedSearch.trim() || selectedResponder !== 'All Responders'
              ? 'No responders match your search criteria'
              : 'There are no responders available'}
          </p>
        </div>
      )}

      {/* View More Modal */}
      <Modal
        opened={isModalOpen}
        onClose={handleCloseModal}
        title=""
        centered
        size="sm"
        styles={{
          header: {
            display: 'none',
          },
          body: {
            padding: 0,
          },
        }}
      >
        {selectedResponderForModal && (
          <div className="p-6">
            {/* Close button */}
            <div className="flex justify-end mb-4">
              <button
                onClick={handleCloseModal}
                className="p-1 hover:bg-gray-100 rounded-full transition-colors"
              >
                <X size={20} className="text-gray-500" />
              </button>
            </div>

            {/* Responder details */}
            <div className="text-center">
              {/* Profile image */}
              <div className="w-20 h-20 mx-auto mb-4 bg-gray-200 rounded-full flex items-center justify-center">
                {selectedResponderForModal.profile_image_url ? (
                  <img
                    src={selectedResponderForModal.profile_image_url}
                    alt="Profile"
                    className="w-full h-full rounded-full object-cover"
                  />
                ) : (
                  <UserSearch size={32} className="text-gray-400" />
                )}
              </div>

              {/* Full name */}
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {`${selectedResponderForModal.first_name || ''} ${selectedResponderForModal.last_name || ''}`.trim() ||
                  'Unknown Responder'}
              </h3>

              {/* Responder type */}
              <p className="text-sm text-gray-600 capitalize">
                {getResponderGroup(selectedResponderForModal.responder_type) ||
                  'Unknown Type'}
              </p>
            </div>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default Responders

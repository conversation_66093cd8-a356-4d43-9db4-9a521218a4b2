/**
 * User Utilities
 *
 * Utility functions for user-related operations
 */

import type { Dispatcher } from '@/types/api/auth'

/**
 * Get user initials from first and last name
 */
export const getUserInitials = (
  firstName?: string,
  lastName?: string
): string => {
  if (!firstName && !lastName) {
    return 'U' // Default to 'U' for User
  }

  const firstInitial = firstName?.charAt(0)?.toUpperCase() || ''
  const lastInitial = lastName?.charAt(0)?.toUpperCase() || ''

  return `${firstInitial}${lastInitial}` || 'U'
}

/**
 * Get user initials from dispatcher data
 */
export const getDispatcherInitials = (dispatcher?: Dispatcher): string => {
  if (!dispatcher) return 'U'

  // Try new format first
  if (dispatcher.first_name || dispatcher.last_name) {
    return getUserInitials(dispatcher.first_name, dispatcher.last_name)
  }

  // Fallback to legacy format
  if (dispatcher.name) {
    const nameParts = dispatcher.name.split(' ')
    const firstName = nameParts[0]
    const lastName = nameParts[nameParts.length - 1]
    return getUserInitials(firstName, lastName)
  }

  return 'U'
}

/**
 * Get full name from dispatcher data
 */
export const getDispatcherFullName = (dispatcher?: Dispatcher): string => {
  if (!dispatcher) return 'User'

  // Try new format first
  if (dispatcher.first_name || dispatcher.last_name) {
    return `${dispatcher.first_name || ''} ${dispatcher.last_name || ''}`.trim()
  }

  // Fallback to legacy format
  if (dispatcher.name) {
    return dispatcher.name
  }

  return 'User'
}

/**
 * Get profile image URL or fallback to initials
 */
export const getDispatcherAvatar = (
  dispatcher?: Dispatcher
): {
  imageUrl?: string
  initials: string
  fullName: string
} => {
  const initials = getDispatcherInitials(dispatcher)
  const fullName = getDispatcherFullName(dispatcher)

  // Try new format first
  const imageUrl = dispatcher?.profile_image_url || dispatcher?.profile_image

  return {
    imageUrl: imageUrl || undefined,
    initials,
    fullName,
  }
}

/**
 * Format phone number for display
 */
export const formatPhoneNumber = (phone?: string): string => {
  if (!phone) return ''

  // Remove any non-digit characters except +
  const cleaned = phone.replace(/[^\d+]/g, '')

  // If it starts with +, keep it
  if (cleaned.startsWith('+')) {
    return cleaned
  }

  // If it's a Nigerian number starting with 234, add +
  if (cleaned.startsWith('234')) {
    return `+${cleaned}`
  }

  // If it's a local Nigerian number starting with 0, convert to international
  if (cleaned.startsWith('0') && cleaned.length === 11) {
    return `+234${cleaned.substring(1)}`
  }

  return cleaned
}

/**
 * Get dispatcher contact info
 */
export const getDispatcherContactInfo = (dispatcher?: Dispatcher) => {
  if (!dispatcher) return null

  return {
    email: dispatcher.email,
    phone: formatPhoneNumber(dispatcher.phone_number || dispatcher.phone),
    address: dispatcher.address_line_1,
    city: dispatcher.city,
    country: dispatcher.country,
  }
}

/**
 * Check if dispatcher profile is complete
 */
export const isDispatcherProfileComplete = (
  dispatcher?: Dispatcher
): boolean => {
  if (!dispatcher) return false

  const requiredFields = [
    dispatcher.first_name || dispatcher.name,
    dispatcher.email,
    dispatcher.phone_number || dispatcher.phone,
    dispatcher.precinct_id,
  ]

  return requiredFields.every(
    field => field && field.toString().trim().length > 0
  )
}

/**
 * Get dispatcher role display name
 */
export const getDispatcherRoleDisplayName = (role?: string): string => {
  switch (role?.toLowerCase()) {
    case 'admin':
      return 'Administrator'
    case 'supervisor':
      return 'Supervisor'
    case 'dispatcher':
      return 'Dispatcher'
    default:
      return 'Dispatcher'
  }
}

/**
 * Get dispatcher status display info
 */
export const getDispatcherStatusInfo = (status?: string) => {
  switch (status?.toLowerCase()) {
    case 'active':
      return {
        label: 'Active',
        color: 'green',
        description: 'Currently active and available',
      }
    case 'inactive':
      return {
        label: 'Inactive',
        color: 'gray',
        description: 'Currently inactive',
      }
    case 'suspended':
      return {
        label: 'Suspended',
        color: 'red',
        description: 'Account suspended',
      }
    default:
      return {
        label: 'Active',
        color: 'green',
        description: 'Currently active and available',
      }
  }
}

import { format, formatDistanceToNow, parseISO } from 'date-fns'
/**
 * Formats a timestamp into a human-readable relative time string
 * @param timestamp - ISO date string or Date object
 * @returns Formatted string like "7:09am (3 hours ago)"
 */
export function formatRelativeTime(timestamp: string | Date): string {
  try {
    const date = typeof timestamp === 'string' ? parseISO(timestamp) : timestamp

    // Validate the date
    if (isNaN(date.getTime())) {
      throw new Error('Invalid date')
    }

    // Format the time part (e.g., "7:09am", "3:19pm")
    const timeString = format(date, 'h:mma').toLowerCase()

    // Get relative time (e.g., "3 hours ago", "10 days ago")
    const relativeTime = formatDistanceToNow(date, { addSuffix: true })

    return `${timeString} (${relativeTime})`
  } catch (error) {
    console.error('Error formatting time:', error)
    return 'Invalid time'
  }
}
